package com.wexl.erp.fees.service;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeStatus;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class FeeDueReportServiceTest {

    @InjectMocks
    private FeeDueReportService feeDueReportService;

    @Test
    void testBuildCsvBodyWithDynamicHeaders() {
        // Create test data
        FeeDto.FeeDueReportRequest request = FeeDto.FeeDueReportRequest.builder()
                .feeGroupTypes(List.of("Admission Fee", "Tuition Fee", "Transport Fee"))
                .build();

        List<FeeDto.FeeDetailResponse> feeDetails = List.of(
                FeeDto.FeeDetailResponse.builder()
                        .feeTypeName("Admission Fee")
                        .month("APR")
                        .balanceAmount(16000.0)
                        .status(FeeStatus.PENDING)
                        .build(),
                FeeDto.FeeDetailResponse.builder()
                        .feeTypeName("Tuition Fee")
                        .month("MAY")
                        .balanceAmount(15000.0)
                        .status(FeeStatus.PENDING)
                        .build(),
                FeeDto.FeeDetailResponse.builder()
                        .feeTypeName("Transport Fee")
                        .month("JUN")
                        .balanceAmount(5000.0)
                        .status(FeeStatus.PENDING)
                        .build()
        );

        List<FeeDto.FeeDueReportResponse> reportData = List.of(
                FeeDto.FeeDueReportResponse.builder()
                        .studentName("John Doe")
                        .admissionNumber("ADM001")
                        .rollNumber("1")
                        .sectionName("A")
                        .dateOfAdmission("01-04-2024")
                        .fatherName("John Sr.")
                        .motherName("Jane Doe")
                        .guardianName("")
                        .mobileNumber("9876543210")
                        .feeDetails(feeDetails)
                        .discountAmount(1000.0)
                        .totalDueAmount(35000.0)
                        .build()
        );

        // Test the method
        List<List<String>> csvBody = feeDueReportService.buildCsvBody(reportData, request);

        // Verify results
        assertNotNull(csvBody);
        assertFalse(csvBody.isEmpty());
        
        // Check headers
        List<String> headers = csvBody.get(0);
        assertTrue(headers.contains("Student Name"));
        assertTrue(headers.contains("Admission Fee"));
        assertTrue(headers.contains("Tuition Fee"));
        assertTrue(headers.contains("Transport Fee"));
        assertTrue(headers.contains("Discount Amount"));
        assertTrue(headers.contains("Total Due"));

        // Check data row
        List<String> dataRow = csvBody.get(1);
        assertEquals("John Doe", dataRow.get(0)); // Student Name
        assertEquals("ADM001", dataRow.get(1)); // Admission Number
        assertEquals("1", dataRow.get(2)); // Roll Number
        assertEquals("A", dataRow.get(3)); // Section
        assertEquals("01-04-2024", dataRow.get(4)); // Date of Admission
        assertEquals("John Sr.", dataRow.get(5)); // Father Name
        assertEquals("Jane Doe", dataRow.get(6)); // Mother Name
        assertEquals("", dataRow.get(7)); // Guardian Name
        assertEquals("9876543210", dataRow.get(8)); // Mobile Number
        
        // Check fee amounts are properly formatted
        assertTrue(dataRow.contains("16000")); // Admission Fee amount
        assertTrue(dataRow.contains("15000")); // Tuition Fee amount
        assertTrue(dataRow.contains("5000")); // Transport Fee amount
        assertTrue(dataRow.contains("1000")); // Discount Amount
        assertTrue(dataRow.contains("35000")); // Total Due Amount
    }

    @Test
    void testBuildCsvBodyWithEmptyData() {
        FeeDto.FeeDueReportRequest request = FeeDto.FeeDueReportRequest.builder()
                .feeGroupTypes(List.of("Admission Fee"))
                .build();

        List<List<String>> csvBody = feeDueReportService.buildCsvBody(List.of(), request);

        assertNotNull(csvBody);
        assertTrue(csvBody.isEmpty());
    }
}
