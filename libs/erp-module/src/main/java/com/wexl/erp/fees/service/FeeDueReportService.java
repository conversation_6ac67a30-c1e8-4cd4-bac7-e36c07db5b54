package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {

    List<FeeDto.FeeDueReportResponse> reportData = generateFeeDueReport(orgSlug, request);
    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    generateCsvResponse(reportData, response, reportType, request);
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request) {

    List<Student> students = getStudentsBySectionUuids(request);
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    return students.stream()
        .map(
            student -> {
              Double discount = calculateDiscount(student);
              return buildFeeDueReportResponse(discount, student, feeHeadsByStudent.get(student));
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(FeeDto.FeeDueReportRequest request) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
            request.sectionUuids().stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  @Async
  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());

    String reportType =
        request.reportType() != null ? request.reportType().toLowerCase() : "total_due";
    return switch (reportType) {
      case "past_due" ->
          feeHeadRepository.findPastDueFeeDetails(
              orgSlug,
              studentIds,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      case "total_due" ->
          feeHeadRepository.findTotalDueFeeDetails(
              orgSlug,
              studentIds,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      default -> Collections.emptyList();
    };
  }

  @Async
  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Double discount, Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
            .fatherName(
                student.getGuardians().stream()
                    .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                    .findAny()
                    .map(x -> x.getFirstName() + " " + x.getLastName())
                    .orElse(null))
        .motherName
            (student.getGuardians().stream()
                    .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                    .findAny()
                    .map(x -> x.getFirstName() + " " + x.getLastName())
                    .orElse(null))
            .guardianName
            (student.getGuardians().stream()
                    .filter(x -> x.getRelationType().equals(GuardianRole.GUARDIAN))
                    .findAny()
                    .map(x -> x.getFirstName() + " " + x.getLastName())
                    .orElse(null))
            .mobileNumber(student.getUserInfo().getMobileNumber())
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(
            student.getCreatedAt() != null
                ? student
                    .getCreatedAt()
                    .toLocalDateTime()
                    .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "")
        .feeDetails(feeDetails)
        .discountAmount(discount)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeType().getDescription())
        .month(
            feeHead.getDueDate() != null
                ? feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toUpperCase()
                : "")
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      String reportType,
      FeeDto.FeeDueReportRequest request) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv");
    response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

    List<List<String>> csvBody = buildCsvBody(reportData, request);
    List<String> csvHeaders = csvBody.isEmpty() ? List.of() : csvBody.removeFirst();

    CsvUtils.generateCsv(csvHeaders.toArray(new String[0]), csvBody, response);
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private List<List<String>> buildCsvBody(List<FeeDto.FeeDueReportResponse> reportData, FeeDto.FeeDueReportRequest request) {
    List<List<String>> csvBody = new ArrayList<>();

    if (reportData.isEmpty()) {
      return csvBody;
    }

    List<String> headers = generateHeaders(reportData, request);
    List<String> columnKeys = generateColumnKeys(reportData, request);

    csvBody.add(headers);

    for (FeeDto.FeeDueReportResponse report : reportData) {
      Map<String, Double> feeAmountsByKey = buildFeeAmountsMap(report);
      List<String> row = buildDataRow(report, columnKeys, feeAmountsByKey);
      csvBody.add(row);
    }

    return csvBody;
  }

  private Map<String, Double> buildFeeAmountsMap(FeeDto.FeeDueReportResponse report) {
    Map<String, Double> feeAmountsByKey = new HashMap<>();

    for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
      String feeGroup = detail.feeTypeName();
      String month = (detail.month() != null && !detail.month().isEmpty()) ? detail.month() : "TOTAL";
      String key = feeGroup + "_" + month;

      feeAmountsByKey.merge(key, detail.balanceAmount() != null ? detail.balanceAmount() : 0.0, Double::sum);
    }

    return feeAmountsByKey;
  }

  private List<String> buildDataRow(FeeDto.FeeDueReportResponse report, List<String> columnKeys, Map<String, Double> feeAmountsByKey) {
    List<String> row = new ArrayList<>();

    for (String columnKey : columnKeys) {
      String value = switch (columnKey) {
        case "STUDENT_NAME" -> report.studentName() != null ? report.studentName() : "";
        case "ADMISSION_NUMBER" -> report.admissionNumber() != null ? report.admissionNumber() : "";
        case "ROLL_NUMBER" -> report.rollNumber() != null ? report.rollNumber() : "";
        case "SECTION" -> report.sectionName() != null ? report.sectionName() : "";
        case "DATE_OF_ADMISSION" -> report.dateOfAdmission() != null ? report.dateOfAdmission() : "";
        case "FATHER_NAME" -> report.fatherName() != null ? report.fatherName() : "";
        case "MOTHER_NAME" -> report.motherName() != null ? report.motherName() : "";
        case "GUARDIAN_NAME" -> report.guardianName() != null ? report.guardianName() : "";
        case "MOBILE_NUMBER" -> report.mobileNumber() != null ? report.mobileNumber() : "";
        case "DISCOUNT_AMOUNT" -> String.format("%.0f", report.discountAmount() != null ? report.discountAmount() : 0.0);
        case "TOTAL_DUE" -> String.format("%.0f", report.totalDueAmount() != null ? report.totalDueAmount() : 0.0);
        default -> {
          Double amount = feeAmountsByKey.getOrDefault(columnKey, 0.0);
          yield String.format("%.0f", amount);
        }
      };
      row.add(value);
    }

    return row;
  }

  private List<String> generateHeaders(List<FeeDto.FeeDueReportResponse> reportData, FeeDto.FeeDueReportRequest request) {
    List<String> headers = new ArrayList<>();

    headers.addAll(List.of("Student Name", "Admission Number", "Roll Number", "Section",
                          "Date of Admission", "Father Name", "Mother Name", "Guardian Name", "Mobile Number"));

    Map<String, Set<String>> feeGroupMonths = getFeeGroupMonths(reportData, request);

    for (Map.Entry<String, Set<String>> entry : feeGroupMonths.entrySet()) {
      String feeGroup = entry.getKey();
      Set<String> months = entry.getValue();

      if (months.isEmpty()) {
        headers.add(feeGroup);
      }
    }

    headers.addAll(List.of("Discount Amount", "Total Due"));

    return headers;
  }

  private List<String> generateColumnKeys(List<FeeDto.FeeDueReportResponse> reportData, FeeDto.FeeDueReportRequest request) {
    List<String> columnKeys = new ArrayList<>();

    columnKeys.addAll(List.of("STUDENT_NAME", "ADMISSION_NUMBER", "ROLL_NUMBER", "SECTION",
                             "DATE_OF_ADMISSION", "FATHER_NAME", "MOTHER_NAME", "GUARDIAN_NAME", "MOBILE_NUMBER"));

    Map<String, Set<String>> feeGroupMonths = getFeeGroupMonths(reportData, request);

    for (Map.Entry<String, Set<String>> entry : feeGroupMonths.entrySet()) {
      String feeGroup = entry.getKey();
      Set<String> months = entry.getValue();

      if (months.isEmpty()) {
        columnKeys.add(feeGroup + "_TOTAL");
      } else {
        for (String month : months) {
          columnKeys.add(feeGroup + "_" + month);
        }
      }
    }

    columnKeys.addAll(List.of("DISCOUNT_AMOUNT", "TOTAL_DUE"));

    return columnKeys;
  }

  private Map<String, Set<String>> getFeeGroupMonths(List<FeeDto.FeeDueReportResponse> reportData, FeeDto.FeeDueReportRequest request) {
    Map<String, Set<String>> feeGroupMonths = new LinkedHashMap<>();

    for (FeeDto.FeeDueReportResponse report : reportData) {
      for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
        String feeGroup = detail.feeTypeName();
        String month = (detail.month() != null && !detail.month().isEmpty()) ? detail.month() : "TOTAL";
        feeGroupMonths.computeIfAbsent(feeGroup, k -> new TreeSet<>()).add(month);
      }
    }

    List<String> requestedFeeGroupTypes = request.feeGroupTypes();
    if (requestedFeeGroupTypes != null && !requestedFeeGroupTypes.isEmpty()) {
      Map<String, Set<String>> filteredFeeGroupMonths = new LinkedHashMap<>();
      for (String requestedType : requestedFeeGroupTypes) {
        filteredFeeGroupMonths.put(requestedType, feeGroupMonths.getOrDefault(requestedType, new TreeSet<>()));
      }
      return filteredFeeGroupMonths;
    }

    return feeGroupMonths;
  }
}
